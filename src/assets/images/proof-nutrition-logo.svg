<svg width="120" height="60" viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="120" height="60" rx="8" fill="url(#logoGrad)"/>
  <text x="60" y="25" text-anchor="middle" fill="white" font-family="Arial" font-size="14" font-weight="bold">PROOF</text>
  <text x="60" y="42" text-anchor="middle" fill="white" font-family="Arial" font-size="10" font-weight="normal">NUTRITION</text>
  <circle cx="20" cy="15" r="3" fill="white" opacity="0.8"/>
  <circle cx="100" cy="45" r="3" fill="white" opacity="0.8"/>
</svg>
