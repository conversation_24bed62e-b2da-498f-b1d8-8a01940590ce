<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title> Промоции</ion-title>
  </ion-toolbar>
  <ion-toolbar *ngIf="selectedSegment === 'supermarkets'" color="dark">
    <ion-row>
      <ion-col
        class="ion-no-padding"
        offset-lg="3"
        offset-xl="4"
        size-lg="6"
        size-xl="4"
      >
        <ion-searchbar
          (ionClear)="handleSearchClear()"
          (ionInput)="handleSearch($event)"
          [debounce]="300"
          placeholder="Търси"
        ></ion-searchbar>
      </ion-col>
    </ion-row>
  </ion-toolbar>
</ion-header>

@if (selectedSegment === 'supermarkets') {
  <ion-fab
    *mpgHasRole="['admin', 'moderator']"

    class="mb"
    horizontal="end"
    slot="fixed"
    vertical="bottom">

    <ion-fab-button>
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
    <ion-fab-list side="top">
      <ion-fab-button
        (click)="handleAddPromotion()"
        [attr.data-desc]="'nutrition.add-promotion' | translate"
      >
        <ion-icon name="pricetag"></ion-icon>
      </ion-fab-button>
      <ion-fab-button
        (click)="handleAddPromotions()"
        [attr.data-desc]="'nutrition.add-promotions' | translate"
      >
        <ion-icon name="pricetags"></ion-icon>
      </ion-fab-button>
    </ion-fab-list>
  </ion-fab>
}
<ion-content>
  <ion-refresher (ionRefresh)="handleRefresh($event)" slot="fixed">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <!-- Segment Control -->
  <ion-segment
    (ionChange)="handleSegmentChange($event)"
    [(ngModel)]="selectedSegment"
    class="segment-control">
    <ion-segment-button value="supermarkets">
      <ion-label>{{ 'nutrition.product-promotions.supermarkets' | translate }}</ion-label>
    </ion-segment-button>
    <ion-segment-button value="supplements">
      <ion-label>{{ 'nutrition.product-promotions.supplements' | translate }}</ion-label>
    </ion-segment-button>
  </ion-segment>

  <!-- Supermarkets Section -->
  <div *ngIf="selectedSegment === 'supermarkets'">
    <mpg-loading-spinner [isLoading]="isLoading"></mpg-loading-spinner>
    <ion-row *ngIf="!isLoading">
      <ion-col class="ion-no-padding" offset-lg="3" size="12" size-lg="6">
        <mpg-product-promotion-store-card
          (onUpdate)="handleSearchClear()"
          *ngFor="let store of stores"
          [store]="store"
        ></mpg-product-promotion-store-card>
      </ion-col>
    </ion-row>
  </div>

  <!-- Supplements Section -->
  <div *ngIf="selectedSegment === 'supplements'">
    <ion-row>
      <ion-col class="ion-no-padding" offset-lg="2" size="12" size-lg="8">
        <mpg-proof-nutrition-products></mpg-proof-nutrition-products>
      </ion-col>
    </ion-row>
  </div>
</ion-content>
