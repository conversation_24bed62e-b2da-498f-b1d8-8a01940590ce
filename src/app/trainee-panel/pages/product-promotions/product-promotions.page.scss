cdk-virtual-scroll-viewport {
  height: 100%;
  width: 100%;
}

cdk-virtual-scroll-viewport::-webkit-scrollbar {
  display: none;
}

cdk-virtual-scroll-viewport {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.segment-control {
  margin: 16px;
  --background: #f8f9fa;
  --background-checked: #667eea;
  --color-checked: white;
  --border-radius: 12px;
  --indicator-color: transparent;

  ion-segment-button {
    --color: #6c757d;
    --color-checked: white;
    --background-checked: #667eea;
    --border-radius: 8px;
    margin: 4px;
    font-weight: 500;

    &.segment-button-checked {
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }
  }
}
