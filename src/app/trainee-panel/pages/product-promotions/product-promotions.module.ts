import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ProductPromotionsPageRoutingModule } from './product-promotions-routing.module';

import { ProductPromotionsPage } from './product-promotions.page';
import { TranslateModule } from '@ngx-translate/core';
import { ProductPromotionStoreCardModule } from '../../../nutrition/components/product-promotion-store-card/product-promotion-store-card.module';
import { HasRoleDirectiveModule } from '../../../auth/directives/has-role-directive.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { ProofNutritionProductsModule } from '../../../nutrition/components/proof-nutrition-products/proof-nutrition-products.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ProductPromotionsPageRoutingModule,
    TranslateModule,
    ProductPromotionStoreCardModule,
    HasRoleDirectiveModule,
    LoadingSpinnerComponentModule,
    ProofNutritionProductsModule,
  ],
  declarations: [ProductPromotionsPage],
})
export class ProductPromotionsPageModule {}
