import { Component, OnInit } from '@angular/core';
import { ProofNutritionProduct, MOCK_PROOF_NUTRITION_PRODUCTS } from '../../models/proof-nutrition-product.model';
import { ToastService } from '../../../shared/services';
import { Clipboard } from '@capacitor/clipboard';

@Component({
  selector: 'mpg-proof-nutrition-products',
  templateUrl: './proof-nutrition-products.component.html',
  styleUrls: ['./proof-nutrition-products.component.scss'],
})
export class ProofNutritionProductsComponent implements OnInit {
  products: ProofNutritionProduct[] = [];
  promoCode = 'MPG10';
  isLoading = true;

  constructor(private toastService: ToastService) {}

  ngOnInit() {
    this.loadProducts();
  }

  private loadProducts() {
    // Simulate API call
    setTimeout(() => {
      this.products = MOCK_PROOF_NUTRITION_PRODUCTS;
      this.isLoading = false;
    }, 1000);
  }

  async copyPromoCode() {
    try {
      await Clipboard.write({
        string: this.promoCode
      });
      this.toastService.showSuccessToast('nutrition.product-promotions.code-copied');
    } catch (error) {
      // Fallback for web
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(this.promoCode);
        this.toastService.showSuccessToast('nutrition.product-promotions.code-copied');
      }
    }
  }

  visitStore() {
    window.open('https://proofnutrition.bg', '_blank');
  }

  getStarArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= Math.floor(rating));
    }
    return stars;
  }

  hasHalfStar(rating: number): boolean {
    return rating % 1 >= 0.5;
  }
}
