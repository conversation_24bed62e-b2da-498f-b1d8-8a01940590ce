<!-- Partnership Header -->
<div class="partnership-header">
  <div class="partnership-content">
    <div class="partnership-logo">
      <ion-img src="/assets/images/proof-nutrition-logo.png" alt="Proof Nutrition"></ion-img>
    </div>
    <div class="partnership-text">
      <h2>{{ 'nutrition.product-promotions.proof-nutrition-partnership' | translate }}</h2>
      <p>{{ 'nutrition.product-promotions.proof-nutrition-description' | translate }}</p>
    </div>
  </div>
  
  <!-- Promo Code Section -->
  <div class="promo-code-section">
    <div class="promo-code-card">
      <div class="promo-code-content">
        <span class="promo-label">{{ 'nutrition.product-promotions.promo-code' | translate }}:</span>
        <span class="promo-code">{{ promoCode }}</span>
      </div>
      <div class="promo-actions">
        <ion-button 
          (click)="copyPromoCode()" 
          fill="outline" 
          size="small"
          color="primary">
          <ion-icon name="copy-outline" slot="start"></ion-icon>
          {{ 'nutrition.product-promotions.copy-code' | translate }}
        </ion-button>
        <ion-button 
          (click)="visitStore()" 
          fill="solid" 
          size="small"
          color="success">
          <ion-icon name="storefront-outline" slot="start"></ion-icon>
          {{ 'nutrition.product-promotions.visit-store' | translate }}
        </ion-button>
      </div>
    </div>
  </div>
</div>

<!-- Loading Spinner -->
<mpg-loading-spinner [isLoading]="isLoading"></mpg-loading-spinner>

<!-- Products Grid -->
<div class="products-grid" *ngIf="!isLoading">
  <div class="product-card" *ngFor="let product of products">
    <div class="product-image-container">
      <ion-img [src]="product.imageUrl" [alt]="product.name"></ion-img>
      <div class="discount-badge">
        -{{ product.discountPercentage }}%
      </div>
    </div>
    
    <div class="product-content">
      <div class="product-category">{{ product.category }}</div>
      <h3 class="product-name">{{ product.name }}</h3>
      <p class="product-description">{{ product.description }}</p>
      
      <!-- Features -->
      <div class="product-features">
        <ion-chip 
          *ngFor="let feature of product.features" 
          color="primary" 
          outline="true"
          size="small">
          {{ feature }}
        </ion-chip>
      </div>
      
      <!-- Rating -->
      <div class="product-rating">
        <div class="stars">
          <ion-icon 
            *ngFor="let star of getStarArray(product.rating)" 
            [name]="star ? 'star' : 'star-outline'"
            [color]="star ? 'warning' : 'medium'">
          </ion-icon>
          <ion-icon 
            *ngIf="hasHalfStar(product.rating)"
            name="star-half"
            color="warning">
          </ion-icon>
        </div>
        <span class="rating-text">{{ product.rating }} ({{ product.reviewsCount }})</span>
      </div>
      
      <!-- Price -->
      <div class="product-price">
        <span class="original-price">{{ product.price | currency:'BGN':'symbol':'1.2-2' }}</span>
        <span class="discount-price">{{ product.discountPrice | currency:'BGN':'symbol':'1.2-2' }}</span>
      </div>
      
      <!-- Stock Status -->
      <div class="stock-status" [class.in-stock]="product.inStock">
        <ion-icon [name]="product.inStock ? 'checkmark-circle' : 'close-circle'"></ion-icon>
        <span>{{ product.inStock ? 'В наличност' : 'Няма в наличност' }}</span>
      </div>
    </div>
  </div>
</div>
